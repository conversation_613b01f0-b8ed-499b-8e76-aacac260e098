<?php
// Suppress any unexpected output
ob_start();
require_once "../config/database.php";
require_once "../config/cors.php";

// Set JSON header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    ob_end_flush();
    exit;
}

// Get form data
$data = $_POST;

// Check required fields
if (empty($data['ID_USER']) || empty($data['TYPE_ANNONCE']) || empty($data['TITRE']) || 
    empty($data['LOCALISATION']) || empty($data['PRIX'])) {
    http_response_code(400);
    echo json_encode(["error" => "Veuillez remplir tous les champs obligatoires"]);
    ob_end_flush();
    exit;
}

// Validate price
if (!is_numeric($data['PRIX']) || $data['PRIX'] <= 0) {
    http_response_code(400);
    echo json_encode(["error" => "Le prix doit être un nombre positif"]);
    ob_end_flush();
    exit;
}

// Handle image uploads
$images = [];
if (isset($_FILES['images']) && is_array($_FILES['images']['name'])) {
    $upload_dir = '../uploads/annonces/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    $allowed = ['image/png', 'image/jpeg', 'image/gif', 'image/jpg'];
    
    for ($i = 0; $i < count($_FILES['images']['name']); $i++) {
        if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
            if (!in_array($_FILES['images']['type'][$i], $allowed) || 
                $_FILES['images']['size'][$i] > 10 * 1024 * 1024) {
                http_response_code(400);
                echo json_encode(["error" => "Fichier image invalide ou trop grand"]);
                ob_end_flush();
                exit;
            }
            
            $file_name = uniqid() . '_' . basename($_FILES['images']['name'][$i]);
            $file_path = $upload_dir . $file_name;
            
            if (move_uploaded_file($_FILES['images']['tmp_name'][$i], $file_path)) {
                $images[] = $file_name;
            }
        }
    }
}

// Handle video upload
$video = null;
if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
    $allowed_video = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
    if (!in_array($_FILES['video']['type'], $allowed_video) || 
        $_FILES['video']['size'] > 50 * 1024 * 1024) {
        http_response_code(400);
        echo json_encode(["error" => "Fichier vidéo invalide ou trop grand (max 50MB)"]);
        ob_end_flush();
        exit;
    }
    
    $upload_dir = '../uploads/annonces/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    $video_name = uniqid() . '_' . basename($_FILES['video']['name']);
    $video_path = $upload_dir . $video_name;
    
    if (move_uploaded_file($_FILES['video']['tmp_name'], $video_path)) {
        $video = $video_name;
    }
}

// Prepare equipments JSON
$equipements = isset($data['equipements']) ? json_encode($data['equipements']) : null;
$images_json = !empty($images) ? json_encode($images) : null;

try {
    $sql = $db->prepare("
        INSERT INTO annonces (
            ID_USER, TYPE_ANNONCE, TITRE, DESCRIPTION, LOCALISATION, PRIX, 
            SUPERFICIE, NB_PIECES, TYPE_DUREE, STATUT_MEUBLE, EQUIPEMENTS, 
            IMAGES, VIDEO
        ) VALUES (
            :id_user, :type_annonce, :titre, :description, :localisation, :prix,
            :superficie, :nb_pieces, :type_duree, :statut_meuble, :equipements,
            :images, :video
        )
    ");
    
    $sql->execute([
        ":id_user" => $data['ID_USER'],
        ":type_annonce" => $data['TYPE_ANNONCE'],
        ":titre" => $data['TITRE'],
        ":description" => $data['DESCRIPTION'] ?? null,
        ":localisation" => $data['LOCALISATION'],
        ":prix" => $data['PRIX'],
        ":superficie" => $data['SUPERFICIE'] ?? null,
        ":nb_pieces" => $data['NB_PIECES'] ?? null,
        ":type_duree" => $data['TYPE_DUREE'] ?? 'mois',
        ":statut_meuble" => $data['STATUT_MEUBLE'] ?? 'non_meuble',
        ":equipements" => $equipements,
        ":images" => $images_json,
        ":video" => $video
    ]);
    
    $annonce_id = $db->lastInsertId();
    
    http_response_code(201);
    echo json_encode([
        "success" => "Annonce créée avec succès",
        "annonce_id" => $annonce_id
    ]);
    
} catch (PDOException $e) {
    error_log("Create annonce error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => "Erreur serveur: " . $e->getMessage()]);
}

ob_end_flush();
exit;
?>
