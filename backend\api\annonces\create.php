<?php
// Suppress any unexpected output
ob_start();
require_once "../config/database.php";
require_once "../config/cors.php";

// Set JSON header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    ob_end_flush();
    exit;
}

// Get form data
$data = $_POST;

// Check required fields
if (
    empty($data['ID_USER']) || empty($data['TYPE_LOC']) ||
    empty($data['VILLE']) || empty($data['PRIX'])
) {
    http_response_code(400);
    echo json_encode(["error" => "Veuillez remplir tous les champs obligatoires"]);
    ob_end_flush();
    exit;
}

// Validate price
if (!is_numeric($data['PRIX']) || $data['PRIX'] <= 0) {
    http_response_code(400);
    echo json_encode(["error" => "Le prix doit être un nombre positif"]);
    ob_end_flush();
    exit;
}

// Handle image upload (single image for POST_IMG)
$post_img = null;
if (isset($_FILES['images']) && is_array($_FILES['images']['name']) && count($_FILES['images']['name']) > 0) {
    $upload_dir = '../uploads/posts/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    $allowed = ['image/png', 'image/jpeg', 'image/gif', 'image/jpg'];

    // Prendre seulement la première image
    if ($_FILES['images']['error'][0] === UPLOAD_ERR_OK) {
        if (
            !in_array($_FILES['images']['type'][0], $allowed) ||
            $_FILES['images']['size'][0] > 10 * 1024 * 1024
        ) {
            http_response_code(400);
            echo json_encode(["error" => "Fichier image invalide ou trop grand"]);
            ob_end_flush();
            exit;
        }

        $file_name = uniqid() . '_' . basename($_FILES['images']['name'][0]);
        $file_path = $upload_dir . $file_name;

        if (move_uploaded_file($_FILES['images']['tmp_name'][0], $file_path)) {
            $post_img = $file_name;
        }
    }
}

// Handle video upload
$post_vid = null;
if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
    $allowed_video = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
    if (
        !in_array($_FILES['video']['type'], $allowed_video) ||
        $_FILES['video']['size'] > 50 * 1024 * 1024
    ) {
        http_response_code(400);
        echo json_encode(["error" => "Fichier vidéo invalide ou trop grand (max 50MB)"]);
        ob_end_flush();
        exit;
    }

    $upload_dir = '../uploads/posts/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    $video_name = uniqid() . '_' . basename($_FILES['video']['name']);
    $video_path = $upload_dir . $video_name;

    if (move_uploaded_file($_FILES['video']['tmp_name'], $video_path)) {
        $post_vid = $video_name;
    }
}

// Prepare equipments
$equipements = isset($data['equipements']) ? implode(',', $data['equipements']) : null;

try {
    $sql = $db->prepare("
        INSERT INTO poste (
            ID_USER, ID_ADMIN, TYPE_LOC, VILLE, QUARTIER, DUREE, PRIX,
            SURFACE, NBRE_PIECE, ETAT, EQUIPEMENT, POST_IMG, POST_VID, DESCRIPTION
        ) VALUES (
            :id_user, :id_admin, :type_loc, :ville, :quartier, :duree, :prix,
            :surface, :nbre_piece, :etat, :equipement, :post_img, :post_vid, :description
        )
    ");

    $sql->execute([
        ":id_user" => $data['ID_USER'],
        ":id_admin" => 1, // ID admin par défaut
        ":type_loc" => $data['TYPE_LOC'],
        ":ville" => $data['VILLE'],
        ":quartier" => $data['QUARTIER'] ?? null,
        ":duree" => $data['DUREE'] ?? null,
        ":prix" => $data['PRIX'],
        ":surface" => $data['SURFACE'] ?? null,
        ":nbre_piece" => $data['NBRE_PIECE'] ?? null,
        ":etat" => $data['ETAT'] ?? null,
        ":equipement" => $equipements,
        ":post_img" => $post_img,
        ":post_vid" => $post_vid,
        ":description" => $data['DESCRIPTION'] ?? null
    ]);

    $post_id = $db->lastInsertId();

    http_response_code(201);
    echo json_encode([
        "success" => "Post créé avec succès",
        "post_id" => $post_id
    ]);

} catch (PDOException $e) {
    error_log("Create annonce error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => "Erreur serveur: " . $e->getMessage()]);
}

ob_end_flush();
exit;
?>