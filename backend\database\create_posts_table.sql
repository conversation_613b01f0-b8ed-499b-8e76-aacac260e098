-- Table pour les posts/annonces
CREATE TABLE `posts` (
  `ID_POST` int(11) NOT NULL AUTO_INCREMENT,
  `ID_USER` int(11) NOT NULL,
  `TYPE_POST` enum('location','vente','colocation','recherche') NOT NULL,
  `TITRE` varchar(255) NOT NULL,
  `DESCRIPTION` longtext DEFAULT NULL,
  `LOCALISATION` varchar(255) NOT NULL,
  `PRIX` decimal(10,2) NOT NULL,
  `SUPERFICIE` int(11) DEFAULT NULL,
  `NB_PIECES` int(11) DEFAULT NULL,
  `TYPE_DUREE` enum('jour','semaine','mois','annee') DEFAULT 'mois',
  `STATUT_MEUBLE` enum('meuble','non_meuble','partiellement_meuble') DEFAULT 'non_meuble',
  `EQUIPEMENTS` longtext DEFAULT NULL, -- JSON des équipements
  `IMAGES` longtext DEFAULT NULL, -- JSON des chemins d'images
  `VIDEO` varchar(255) DEFAULT NULL,
  `DATE_CREATION` timestamp NOT NULL DEFAULT current_timestamp(),
  `DATE_MODIFICATION` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `STATUT` enum('active','inactive','supprimee') DEFAULT 'active',
  `VUES` int(11) DEFAULT 0,
  `FAVORIS` int(11) DEFAULT 0,
  PRIMARY KEY (`ID_POST`),
  KEY `FK_POST_USER` (`ID_USER`),
  CONSTRAINT `FK_POST_USER` FOREIGN KEY (`ID_USER`) REFERENCES `user` (`ID_USER`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table pour les likes/favoris des posts
CREATE TABLE `post_likes` (
  `ID_LIKE` int(11) NOT NULL AUTO_INCREMENT,
  `ID_POST` int(11) NOT NULL,
  `ID_USER` int(11) NOT NULL,
  `DATE_LIKE` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`ID_LIKE`),
  UNIQUE KEY `unique_like` (`ID_POST`, `ID_USER`),
  KEY `FK_LIKE_POST` (`ID_POST`),
  KEY `FK_LIKE_USER` (`ID_USER`),
  CONSTRAINT `FK_LIKE_POST` FOREIGN KEY (`ID_POST`) REFERENCES `posts` (`ID_POST`) ON DELETE CASCADE,
  CONSTRAINT `FK_LIKE_USER` FOREIGN KEY (`ID_USER`) REFERENCES `user` (`ID_USER`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table pour les commentaires des posts
CREATE TABLE `post_commentaires` (
  `ID_COMMENTAIRE` int(11) NOT NULL AUTO_INCREMENT,
  `ID_POST` int(11) NOT NULL,
  `ID_USER` int(11) NOT NULL,
  `COMMENTAIRE` longtext NOT NULL,
  `DATE_COMMENTAIRE` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`ID_COMMENTAIRE`),
  KEY `FK_COMMENT_POST` (`ID_POST`),
  KEY `FK_COMMENT_USER` (`ID_USER`),
  CONSTRAINT `FK_COMMENT_POST` FOREIGN KEY (`ID_POST`) REFERENCES `posts` (`ID_POST`) ON DELETE CASCADE,
  CONSTRAINT `FK_COMMENT_USER` FOREIGN KEY (`ID_USER`) REFERENCES `user` (`ID_USER`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
