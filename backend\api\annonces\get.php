<?php
require_once "../config/database.php";
require_once "../config/cors.php";

// Set JSON header
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    exit;
}

try {
    // Get query parameters
    $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    
    // Build query
    $where_clause = "WHERE a.STATUT = 'active'";
    $params = [];
    
    if ($user_id) {
        $where_clause .= " AND a.ID_USER = :user_id";
        $params[':user_id'] = $user_id;
    }
    
    $sql = "
        SELECT 
            a.*,
            u.NOM as AUTEUR_NOM,
            u.IMG_PROFIL as AUTEUR_AVATAR,
            (SELECT COUNT(*) FROM annonce_likes al WHERE al.ID_ANNONCE = a.ID_ANNONCE) as LIKES_COUNT,
            (SELECT COUNT(*) FROM annonce_commentaires ac WHERE ac.ID_ANNONCE = a.ID_ANNONCE) as COMMENTS_COUNT
        FROM annonces a
        JOIN user u ON a.ID_USER = u.ID_USER
        $where_clause
        ORDER BY a.DATE_CREATION DESC
        LIMIT :limit OFFSET :offset
    ";
    
    $stmt = $db->prepare($sql);
    
    // Bind parameters
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    
    $stmt->execute();
    $annonces = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the results
    foreach ($annonces as &$annonce) {
        // Decode JSON fields
        $annonce['EQUIPEMENTS'] = $annonce['EQUIPEMENTS'] ? json_decode($annonce['EQUIPEMENTS'], true) : [];
        $annonce['IMAGES'] = $annonce['IMAGES'] ? json_decode($annonce['IMAGES'], true) : [];
        
        // Format dates
        $annonce['DATE_CREATION_FORMATTED'] = date('d/m/Y H:i', strtotime($annonce['DATE_CREATION']));
        
        // Calculate time ago
        $time_diff = time() - strtotime($annonce['DATE_CREATION']);
        if ($time_diff < 60) {
            $annonce['TIME_AGO'] = 'à l\'instant';
        } elseif ($time_diff < 3600) {
            $minutes = floor($time_diff / 60);
            $annonce['TIME_AGO'] = "il y a $minutes minute" . ($minutes > 1 ? 's' : '');
        } elseif ($time_diff < 86400) {
            $hours = floor($time_diff / 3600);
            $annonce['TIME_AGO'] = "il y a $hours heure" . ($hours > 1 ? 's' : '');
        } else {
            $days = floor($time_diff / 86400);
            $annonce['TIME_AGO'] = "il y a $days jour" . ($days > 1 ? 's' : '');
        }
        
        // Format price
        $annonce['PRIX_FORMATTED'] = number_format($annonce['PRIX'], 0, ',', ' ') . '€';
        
        // Build description text
        $type_text = [
            'location' => 'Location',
            'vente' => 'Vente',
            'colocation' => 'Colocation',
            'recherche' => 'Recherche'
        ];
        
        $meuble_text = [
            'meuble' => 'Meublé',
            'non_meuble' => 'Non meublé',
            'partiellement_meuble' => 'Partiellement meublé'
        ];
        
        $annonce['TYPE_TEXT'] = $type_text[$annonce['TYPE_ANNONCE']] ?? $annonce['TYPE_ANNONCE'];
        $annonce['MEUBLE_TEXT'] = $meuble_text[$annonce['STATUT_MEUBLE']] ?? $annonce['STATUT_MEUBLE'];
        
        // Build content summary
        $content_parts = [];
        $content_parts[] = $annonce['TYPE_TEXT'];
        $content_parts[] = $annonce['LOCALISATION'];
        $content_parts[] = $annonce['PRIX_FORMATTED'] . '/' . $annonce['TYPE_DUREE'];
        
        if ($annonce['SUPERFICIE']) {
            $content_parts[] = $annonce['SUPERFICIE'] . 'm²';
        }
        if ($annonce['NB_PIECES']) {
            $content_parts[] = $annonce['NB_PIECES'] . ' pièces';
        }
        $content_parts[] = $annonce['MEUBLE_TEXT'];
        
        $annonce['CONTENT_SUMMARY'] = implode(' - ', $content_parts);
    }
    
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "annonces" => $annonces,
        "total" => count($annonces)
    ]);
    
} catch (PDOException $e) {
    error_log("Get annonces error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(["error" => "Erreur serveur: " . $e->getMessage()]);
}

exit;
?>
