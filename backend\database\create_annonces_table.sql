-- Table pour les annonces
CREATE TABLE `annonces` (
  `ID_ANNONCE` int(11) NOT NULL AUTO_INCREMENT,
  `ID_USER` int(11) NOT NULL,
  `TYPE_ANNONCE` enum('location','vente','colocation','recherche') NOT NULL,
  `TITRE` varchar(255) NOT NULL,
  `DESCRIPTION` longtext DEFAULT NULL,
  `LOCALISATION` varchar(255) NOT NULL,
  `PRIX` decimal(10,2) NOT NULL,
  `SUPERFICIE` int(11) DEFAULT NULL,
  `NB_PIECES` int(11) DEFAULT NULL,
  `TYPE_DUREE` enum('jour','semaine','mois','annee') DEFAULT 'mois',
  `STATUT_MEUBLE` enum('meuble','non_meuble','partiellement_meuble') DEFAULT 'non_meuble',
  `EQUIPEMENTS` longtext DEFAULT NULL, -- JSON des équipements
  `IMAGES` longtext DEFAULT NULL, -- JSON des chemins d'images
  `VIDEO` varchar(255) DEFAULT NULL,
  `DATE_CREATION` timestamp NOT NULL DEFAULT current_timestamp(),
  `DATE_MODIFICATION` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `STATUT` enum('active','inactive','supprimee') DEFAULT 'active',
  `VUES` int(11) DEFAULT 0,
  `FAVORIS` int(11) DEFAULT 0,
  PRIMARY KEY (`ID_ANNONCE`),
  KEY `FK_ANNONCE_USER` (`ID_USER`),
  CONSTRAINT `FK_ANNONCE_USER` FOREIGN KEY (`ID_USER`) REFERENCES `user` (`ID_USER`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table pour les likes/favoris des annonces
CREATE TABLE `annonce_likes` (
  `ID_LIKE` int(11) NOT NULL AUTO_INCREMENT,
  `ID_ANNONCE` int(11) NOT NULL,
  `ID_USER` int(11) NOT NULL,
  `DATE_LIKE` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`ID_LIKE`),
  UNIQUE KEY `unique_like` (`ID_ANNONCE`, `ID_USER`),
  KEY `FK_LIKE_ANNONCE` (`ID_ANNONCE`),
  KEY `FK_LIKE_USER` (`ID_USER`),
  CONSTRAINT `FK_LIKE_ANNONCE` FOREIGN KEY (`ID_ANNONCE`) REFERENCES `annonces` (`ID_ANNONCE`) ON DELETE CASCADE,
  CONSTRAINT `FK_LIKE_USER` FOREIGN KEY (`ID_USER`) REFERENCES `user` (`ID_USER`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table pour les commentaires des annonces
CREATE TABLE `annonce_commentaires` (
  `ID_COMMENTAIRE` int(11) NOT NULL AUTO_INCREMENT,
  `ID_ANNONCE` int(11) NOT NULL,
  `ID_USER` int(11) NOT NULL,
  `COMMENTAIRE` longtext NOT NULL,
  `DATE_COMMENTAIRE` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`ID_COMMENTAIRE`),
  KEY `FK_COMMENT_ANNONCE` (`ID_ANNONCE`),
  KEY `FK_COMMENT_USER` (`ID_USER`),
  CONSTRAINT `FK_COMMENT_ANNONCE` FOREIGN KEY (`ID_ANNONCE`) REFERENCES `annonces` (`ID_ANNONCE`) ON DELETE CASCADE,
  CONSTRAINT `FK_COMMENT_USER` FOREIGN KEY (`ID_USER`) REFERENCES `user` (`ID_USER`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
