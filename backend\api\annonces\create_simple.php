<?php
// Version simplifiée pour debug
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: http://localhost:5173");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(["error" => "Méthode non autorisée"]);
    exit;
}

try {
    // Configuration de la base de données directement ici
    $host = 'localhost';
    $dbname = 'localbook';
    $username = 'root';
    $password = '';
    
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get form data
    $data = $_POST;
    
    // Validation simple
    if (empty($data['ID_USER']) || empty($data['TYPE_LOC']) || empty($data['VILLE']) || empty($data['PRIX'])) {
        echo json_encode([
            "error" => "Champs obligatoires manquants",
            "received" => $data
        ]);
        exit;
    }
    
    // Insert simple
    $sql = $db->prepare("
        INSERT INTO poste (ID_USER, ID_ADMIN, TYPE_LOC, VILLE, PRIX, DESCRIPTION) 
        VALUES (:id_user, 1, :type_loc, :ville, :prix, :description)
    ");
    
    $result = $sql->execute([
        ':id_user' => $data['ID_USER'],
        ':type_loc' => $data['TYPE_LOC'],
        ':ville' => $data['VILLE'],
        ':prix' => $data['PRIX'],
        ':description' => $data['DESCRIPTION'] ?? 'Test post'
    ]);
    
    if ($result) {
        echo json_encode([
            "success" => "Post créé avec succès",
            "post_id" => $db->lastInsertId()
        ]);
    } else {
        echo json_encode([
            "error" => "Échec de l'insertion"
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        "error" => "Erreur: " . $e->getMessage()
    ]);
}
?>
