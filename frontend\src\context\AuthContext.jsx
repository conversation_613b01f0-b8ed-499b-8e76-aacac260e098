import React, { createContext, useState, useContext } from 'react';
import AuthService from '../services/AuthService';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [user, setUser] = useState(null);

  const register = async (userData, cinFile) => {
    setError(null);
    setSuccess(null);
    const result = await AuthService.register(userData, cinFile);
    if (result.success) {
      setSuccess(result.message);
    } else {
      setError(result.error);
    }
    return result;
  };

  const login = async (credentials) => {
    setError(null);
    setSuccess(null);
    try {
      const result = await AuthService.login(credentials);
      if (result.success) {
        setSuccess(result.message);
        setUser(result.user);
      } else {
        setError(result.error);
      }
      return result;
    } catch (error) {
      console.error('Login error:', error);
      setError('Erreur de connexion');
      return { success: false, error: 'Erreur inconnue' };
    }
  };

  return (
    <AuthContext.Provider value={{ register, login, error, success, user, setError, setSuccess }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
